/**
 * 屏幕录制管理器
 * 基于RRWebPlugin实现屏幕录制功能
 */

export interface RecordingSession {
  id: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  events: any[];
  metadata: {
    url: string;
    userAgent: string;
    viewport: {
      width: number;
      height: number;
    };
    userId?: string;
    sessionId?: string;
  };
}

export interface RecordingStats {
  totalSessions: number;
  totalDuration: number; // 总录制时长(秒)
  totalEvents: number;
  oldestSession?: Date;
  newestSession?: Date;
  totalSize: number; // 总存储大小(字节)
}

export class ScreenRecordingManager {
  private readonly STORAGE_KEY_PREFIX = 'cdss-recording-';
  private readonly MAX_STORAGE_SIZE = 100 * 1024 * 1024; // 100MB
  private readonly MAX_SESSIONS = 50; // 最大保存50个录制会话
  
  private currentSession: RecordingSession | null = null;
  private isRecording = false;
  private recordingStartTime: number = 0;

  /**
   * 开始屏幕录制
   */
  public startRecording(): string {
    if (this.isRecording) {
      throw new Error('录制已在进行中');
    }

    const sessionId = `recording-${Date.now()}`;
    const startTime = new Date().toISOString();
    
    this.currentSession = {
      id: sessionId,
      startTime,
      events: [],
      metadata: {
        url: window.location.href,
        userAgent: navigator.userAgent,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        userId: localStorage.getItem('cdss-gc-username') || 'anonymous',
        sessionId: localStorage.getItem('cdss-session-id') || 'unknown'
      }
    };

    this.isRecording = true;
    this.recordingStartTime = Date.now();

    // 保存录制状态
    localStorage.setItem('cdss-recording-active', 'true');
    localStorage.setItem('cdss-current-recording-id', sessionId);

    console.log('Screen recording started:', sessionId);
    return sessionId;
  }

  /**
   * 停止屏幕录制
   */
  public stopRecording(): RecordingSession | null {
    if (!this.isRecording || !this.currentSession) {
      throw new Error('当前没有进行中的录制');
    }

    const endTime = new Date().toISOString();
    const duration = Math.round((Date.now() - this.recordingStartTime) / 1000);

    // 从RRWebPlugin获取录制事件
    const events = this.getRRWebEvents();

    this.currentSession.endTime = endTime;
    this.currentSession.duration = duration;
    this.currentSession.events = events;

    // 保存录制会话
    this.saveRecordingSession(this.currentSession);

    const completedSession = { ...this.currentSession };
    
    // 重置状态
    this.currentSession = null;
    this.isRecording = false;
    this.recordingStartTime = 0;

    // 清除录制状态
    localStorage.removeItem('cdss-recording-active');
    localStorage.removeItem('cdss-current-recording-id');

    console.log('Screen recording stopped:', completedSession.id, `Duration: ${duration}s`);
    return completedSession;
  }

  /**
   * 获取当前录制状态
   */
  public getRecordingStatus(): {
    isRecording: boolean;
    currentSessionId?: string;
    duration?: number;
  } {
    return {
      isRecording: this.isRecording,
      currentSessionId: this.currentSession?.id,
      duration: this.isRecording ? Math.round((Date.now() - this.recordingStartTime) / 1000) : undefined
    };
  }

  /**
   * 从RRWebPlugin获取录制事件
   */
  private getRRWebEvents(): any[] {
    try {
      // 通过PageSpy的事件系统获取rrweb事件
      // 这些事件会被DataHarborPlugin收集
      if (window.$harbor && window.$harbor.getOfflineLog) {
        const allLogs = window.$harbor.getOfflineLog();
        return allLogs.filter((log: any) => log.type === 'rrweb-event');
      }
      return [];
    } catch (error) {
      console.error('Failed to get RRWeb events:', error);
      return [];
    }
  }

  /**
   * 保存录制会话到本地存储
   */
  private saveRecordingSession(session: RecordingSession): void {
    try {
      const key = `${this.STORAGE_KEY_PREFIX}${session.id}`;
      const data = JSON.stringify(session);
      
      // 检查存储空间
      this.checkStorageAndCleanup();
      
      localStorage.setItem(key, data);
      console.log(`Recording session saved: ${session.id}, Events: ${session.events.length}`);
    } catch (error) {
      console.error('Failed to save recording session:', error);
      throw error;
    }
  }

  /**
   * 获取所有录制会话
   */
  public getAllRecordingSessions(): RecordingSession[] {
    const sessions: RecordingSession[] = [];
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(this.STORAGE_KEY_PREFIX)) {
        try {
          const data = localStorage.getItem(key);
          if (data) {
            const session = JSON.parse(data) as RecordingSession;
            sessions.push(session);
          }
        } catch (error) {
          console.error(`Error parsing recording session for key ${key}:`, error);
          localStorage.removeItem(key); // 删除损坏的数据
        }
      }
    }
    
    // 按时间排序，最新的在前
    return sessions.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());
  }

  /**
   * 根据ID获取录制会话
   */
  public getRecordingSession(sessionId: string): RecordingSession | null {
    try {
      const key = `${this.STORAGE_KEY_PREFIX}${sessionId}`;
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get recording session:', error);
      return null;
    }
  }

  /**
   * 删除录制会话
   */
  public deleteRecordingSession(sessionId: string): boolean {
    try {
      const key = `${this.STORAGE_KEY_PREFIX}${sessionId}`;
      localStorage.removeItem(key);
      console.log(`Recording session deleted: ${sessionId}`);
      return true;
    } catch (error) {
      console.error('Failed to delete recording session:', error);
      return false;
    }
  }

  /**
   * 导出录制会话为JSON文件
   */
  public exportRecordingSession(sessionId: string): void {
    const session = this.getRecordingSession(sessionId);
    if (!session) {
      throw new Error('录制会话不存在');
    }

    const exportData = {
      ...session,
      exportTime: new Date().toISOString(),
      version: '1.0.0',
      type: 'rrweb-recording'
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cdss-recording-${sessionId}-${session.startTime.split('T')[0]}.json`;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log(`Recording session exported: ${sessionId}`);
  }

  /**
   * 获取录制统计信息
   */
  public getRecordingStats(): RecordingStats {
    const sessions = this.getAllRecordingSessions();
    
    let totalDuration = 0;
    let totalEvents = 0;
    let totalSize = 0;
    let oldestSession: Date | undefined;
    let newestSession: Date | undefined;

    sessions.forEach(session => {
      totalDuration += session.duration || 0;
      totalEvents += session.events.length;
      
      const sessionDate = new Date(session.startTime);
      if (!oldestSession || sessionDate < oldestSession) {
        oldestSession = sessionDate;
      }
      if (!newestSession || sessionDate > newestSession) {
        newestSession = sessionDate;
      }

      // 估算存储大小
      const sessionData = JSON.stringify(session);
      totalSize += new Blob([sessionData]).size;
    });

    return {
      totalSessions: sessions.length,
      totalDuration,
      totalEvents,
      oldestSession,
      newestSession,
      totalSize
    };
  }

  /**
   * 检查存储空间并清理
   */
  private checkStorageAndCleanup(): void {
    const sessions = this.getAllRecordingSessions();
    
    // 如果会话数量超过限制，删除最旧的
    if (sessions.length >= this.MAX_SESSIONS) {
      const sessionsToDelete = sessions.slice(this.MAX_SESSIONS - 1);
      sessionsToDelete.forEach(session => {
        this.deleteRecordingSession(session.id);
      });
      console.log(`Cleaned up ${sessionsToDelete.length} old recording sessions`);
    }

    // 检查存储大小
    const stats = this.getRecordingStats();
    if (stats.totalSize > this.MAX_STORAGE_SIZE) {
      // 删除最旧的25%会话
      const deleteCount = Math.floor(sessions.length * 0.25);
      const sessionsToDelete = sessions.slice(-deleteCount);
      sessionsToDelete.forEach(session => {
        this.deleteRecordingSession(session.id);
      });
      console.log(`Cleaned up ${deleteCount} recording sessions due to storage limit`);
    }
  }

  /**
   * 清理过期的录制会话（30天前的）
   */
  public cleanupExpiredRecordings(): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - 30);
    
    const sessions = this.getAllRecordingSessions();
    let cleanedCount = 0;
    
    sessions.forEach(session => {
      const sessionDate = new Date(session.startTime);
      if (sessionDate < cutoffDate) {
        this.deleteRecordingSession(session.id);
        cleanedCount++;
      }
    });
    
    if (cleanedCount > 0) {
      console.log(`Cleaned up ${cleanedCount} expired recording sessions`);
    }
  }
}
