/**
 * 日志导出功能
 */

/**
 * 导出当前会话日志
 */
export const exportCurrentSession = async (): Promise<void> => {
  try {
    if (window.$harbor) {
      // 使用DataHarborPlugin的下载功能
      await window.$harbor.onOfflineLog('download');
      console.log('Current session exported successfully');
    } else {
      console.error('DataHarborPlugin not initialized');
      throw new Error('日志系统未初始化，请刷新页面重试');
    }
  } catch (error) {
    console.error('Failed to export current session:', error);
    throw error;
  }
};

/**
 * 自定义格式导出当前会话
 */
export const exportCurrentSessionCustom = async (): Promise<void> => {
  try {
    const sessionData = {
      exportTime: new Date().toISOString(),
      sessionId: localStorage.getItem('cdss-session-id') || 'unknown',
      username: localStorage.getItem('cdss-gc-username') || 'anonymous',
      project: 'CDSS',
      version: '1.0.0',
      userAgent: navigator.userAgent,
      url: window.location.href,
      // 这里可以添加更多会话相关信息
    };
    
    const blob = new Blob([JSON.stringify(sessionData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cdss-session-${Date.now()}.json`;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log('Custom session data exported successfully');
  } catch (error) {
    console.error('Failed to export custom session data:', error);
    throw error;
  }
};

/**
 * 导出历史日志
 */
export const exportHistoryLogs = async (startDate: Date, endDate: Date): Promise<void> => {
  try {
    const historyLogs: any[] = [];
    const STORAGE_KEY_PREFIX = 'cdss-logs-';
    
    // 从localStorage中获取指定日期范围的日志
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(STORAGE_KEY_PREFIX)) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          const logDate = new Date(data.timestamp);
          
          // 检查日期是否在指定范围内
          if (logDate >= startDate && logDate <= endDate) {
            historyLogs.push({
              key,
              timestamp: data.timestamp,
              data: data
            });
          }
        } catch (error) {
          console.error(`Error parsing log data for key ${key}:`, error);
        }
      }
    }
    
    // 按时间排序
    historyLogs.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
    
    if (historyLogs.length === 0) {
      throw new Error('指定日期范围内没有找到日志数据');
    }
    
    // 创建导出数据
    const exportData = {
      exportTime: new Date().toISOString(),
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString()
      },
      totalEntries: historyLogs.length,
      username: localStorage.getItem('cdss-gc-username') || 'anonymous',
      project: 'CDSS',
      logs: historyLogs
    };
    
    // 创建并下载文件
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cdss-history-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.json`;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log(`History logs exported successfully: ${historyLogs.length} entries`);
  } catch (error) {
    console.error('Failed to export history logs:', error);
    throw error;
  }
};

/**
 * 获取可用的历史日志日期范围
 */
export const getAvailableLogDateRange = (): { start: Date | null, end: Date | null, totalEntries: number } => {
  let startDate: Date | null = null;
  let endDate: Date | null = null;
  let totalEntries = 0;

  try {
    // 首先尝试从DataHarborPlugin获取数据
    if (window.$harbor && window.$harbor.getOfflineLog) {
      const harborData = window.$harbor.getOfflineLog();
      if (harborData && Array.isArray(harborData) && harborData.length > 0) {
        harborData.forEach((entry: any) => {
          try {
            const logDate = new Date(entry.time || entry.timestamp || Date.now());

            if (!startDate || logDate < startDate) {
              startDate = logDate;
            }
            if (!endDate || logDate > endDate) {
              endDate = logDate;
            }
            totalEntries++;
          } catch (error) {
            console.error('Error parsing harbor log entry:', error);
          }
        });

        if (totalEntries > 0) {
          return { start: startDate, end: endDate, totalEntries };
        }
      }
    }

    // 如果DataHarborPlugin没有数据，回退到localStorage检查
    const STORAGE_KEY_PREFIX = 'cdss-logs-';

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(STORAGE_KEY_PREFIX)) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          const logDate = new Date(data.timestamp || data.time || Date.now());

          if (!startDate || logDate < startDate) {
            startDate = logDate;
          }
          if (!endDate || logDate > endDate) {
            endDate = logDate;
          }
          totalEntries++;
        } catch (error) {
          console.error(`Error parsing log data for key ${key}:`, error);
        }
      }
    }

    // 检查PageSpy相关的存储键
    const pagespyKeys = ['page-spy-data', 'pagespy-data', 'harbor-data'];
    for (const keyName of pagespyKeys) {
      try {
        const data = localStorage.getItem(keyName);
        if (data) {
          const parsed = JSON.parse(data);
          if (Array.isArray(parsed) && parsed.length > 0) {
            parsed.forEach((entry: any) => {
              try {
                const logDate = new Date(entry.time || entry.timestamp || Date.now());

                if (!startDate || logDate < startDate) {
                  startDate = logDate;
                }
                if (!endDate || logDate > endDate) {
                  endDate = logDate;
                }
                totalEntries++;
              } catch (error) {
                console.error('Error parsing pagespy log entry:', error);
              }
            });
          }
        }
      } catch (error) {
        console.error(`Error parsing data for key ${keyName}:`, error);
      }
    }

  } catch (error) {
    console.error('Error getting available log date range:', error);
  }

  return { start: startDate, end: endDate, totalEntries };
};

/**
 * 导出CSV格式的日志
 */
export const exportLogsAsCSV = async (startDate: Date, endDate: Date): Promise<void> => {
  try {
    const historyLogs: any[] = [];
    const STORAGE_KEY_PREFIX = 'cdss-logs-';
    
    // 获取日志数据
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith(STORAGE_KEY_PREFIX)) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}');
          const logDate = new Date(data.timestamp);
          
          if (logDate >= startDate && logDate <= endDate) {
            historyLogs.push(data);
          }
        } catch (error) {
          console.error(`Error parsing log data for key ${key}:`, error);
        }
      }
    }
    
    if (historyLogs.length === 0) {
      throw new Error('指定日期范围内没有找到日志数据');
    }
    
    // 转换为CSV格式
    const csvHeaders = ['Timestamp', 'Type', 'Message', 'URL', 'User'];
    const csvRows = historyLogs.map(log => [
      log.timestamp || '',
      log.type || '',
      JSON.stringify(log.data || '').replace(/"/g, '""'), // 转义双引号
      log.url || '',
      log.user || ''
    ]);
    
    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
    ].join('\n');
    
    // 创建并下载CSV文件
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cdss-logs-${startDate.toISOString().split('T')[0]}-to-${endDate.toISOString().split('T')[0]}.csv`;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    console.log(`CSV logs exported successfully: ${historyLogs.length} entries`);
  } catch (error) {
    console.error('Failed to export CSV logs:', error);
    throw error;
  }
};
