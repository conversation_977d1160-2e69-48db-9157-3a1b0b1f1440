import React, { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import { getAvailableLogDateRange } from '../utils/logExport';

interface HistoryExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (startDate: Date, endDate: Date, format: 'json' | 'csv') => void;
}

export const HistoryExportModal: React.FC<HistoryExportModalProps> = ({
  isOpen,
  onClose,
  onExport
}) => {
  const [startDate, setStartDate] = useState(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000));
  const [endDate, setEndDate] = useState(new Date());
  const [format, setFormat] = useState<'json' | 'csv'>('json');
  const [availableRange, setAvailableRange] = useState<{
    start: Date | null;
    end: Date | null;
    totalEntries: number;
  }>({ start: null, end: null, totalEntries: 0 });

  // 获取可用的日志日期范围
  useEffect(() => {
    if (isOpen) {
      const range = getAvailableLogDateRange();
      setAvailableRange(range);
      
      // 如果有可用数据，设置默认日期范围
      if (range.start && range.end) {
        setStartDate(range.start);
        setEndDate(range.end);
      }
    }
  }, [isOpen]);

  const handleExport = () => {
    onExport(startDate, endDate, format);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">导出历史日志</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 可用数据信息 */}
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            {availableRange.totalEntries > 0 ? (
              <>
                共找到 <span className="font-semibold">{availableRange.totalEntries}</span> 条日志记录
                {availableRange.start && availableRange.end && (
                  <>
                    <br />
                    时间范围: {availableRange.start.toLocaleDateString()} 至 {availableRange.end.toLocaleDateString()}
                  </>
                )}
              </>
            ) : (
              '暂无可用的历史日志数据'
            )}
          </p>
        </div>

        <div className="space-y-4">
          {/* 开始日期 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              开始日期
            </label>
            <DatePicker
              selected={startDate}
              onChange={(date) => date && setStartDate(date)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              maxDate={endDate}
              minDate={availableRange.start || undefined}
              dateFormat="yyyy-MM-dd"
              placeholderText="选择开始日期"
            />
          </div>

          {/* 结束日期 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              结束日期
            </label>
            <DatePicker
              selected={endDate}
              onChange={(date) => date && setEndDate(date)}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              minDate={startDate}
              maxDate={availableRange.end || new Date()}
              dateFormat="yyyy-MM-dd"
              placeholderText="选择结束日期"
            />
          </div>

          {/* 导出格式 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              导出格式
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="json"
                  checked={format === 'json'}
                  onChange={(e) => setFormat(e.target.value as 'json' | 'csv')}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">JSON</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="csv"
                  checked={format === 'csv'}
                  onChange={(e) => setFormat(e.target.value as 'json' | 'csv')}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">CSV</span>
              </label>
            </div>
          </div>

          {/* 预览信息 */}
          <div className="p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600">
              将导出从 <span className="font-medium">{startDate.toLocaleDateString()}</span> 到{' '}
              <span className="font-medium">{endDate.toLocaleDateString()}</span> 的日志数据
            </p>
            <p className="text-xs text-gray-500 mt-1">
              格式: {format.toUpperCase()}
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleExport}
            disabled={availableRange.totalEntries === 0}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            导出
          </button>
        </div>
      </div>
    </div>
  );
};
