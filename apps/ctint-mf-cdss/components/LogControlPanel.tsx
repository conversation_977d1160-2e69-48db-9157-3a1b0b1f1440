import React, { useState, useRef } from 'react';
import { useDrag } from '@use-gesture/react';
import { HistoryExportModal } from './HistoryExportModal';
import { UploadProgress } from '../utils/serverUpload';

interface LogControlPanelProps {
  onStartRecording: () => void;
  onStopRecording: () => void;
  onExportCurrent: () => Promise<void>;
  onExportHistory: (startDate: Date, endDate: Date, format: 'json' | 'csv') => Promise<void>;
  onUploadToServer: () => Promise<void>;
  isRecording: boolean;
  isInitialized: boolean;
}

export const LogControlPanel: React.FC<LogControlPanelProps> = ({
  onStartRecording,
  onStopRecording,
  onExportCurrent,
  onExportHistory,
  onUploadToServer,
  isRecording,
  isInitialized
}) => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [showHistoryModal, setShowHistoryModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error' | 'info';
    message: string;
  } | null>(null);

  const panelRef = useRef<HTMLDivElement>(null);

  // 拖拽功能
  const bind = useDrag(({ offset: [x, y], dragging }) => {
    if (dragging) {
      setPosition({ x, y });
    }
  }, {
    from: () => [position.x, position.y],
  });

  // 显示通知
  const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  // 处理当前会话导出
  const handleExportCurrent = async () => {
    setIsLoading(true);
    try {
      await onExportCurrent();
      showNotification('success', '当前会话日志导出成功');
    } catch (error) {
      showNotification('error', error instanceof Error ? error.message : '导出失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理历史日志导出
  const handleExportHistory = async (startDate: Date, endDate: Date, format: 'json' | 'csv') => {
    setIsLoading(true);
    try {
      await onExportHistory(startDate, endDate, format);
      showNotification('success', '历史日志导出成功');
    } catch (error) {
      showNotification('error', error instanceof Error ? error.message : '导出失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 处理服务器上传
  const handleUploadToServer = async () => {
    setIsLoading(true);
    setUploadProgress({ percentage: 0, status: 'uploading', message: '开始上传...' });
    
    try {
      await onUploadToServer();
      setUploadProgress({ percentage: 100, status: 'completed', message: '上传完成' });
      showNotification('success', '日志已成功上传到服务器');
    } catch (error) {
      setUploadProgress({ percentage: 0, status: 'error', message: '上传失败' });
      showNotification('error', error instanceof Error ? error.message : '上传失败');
    } finally {
      setIsLoading(false);
      setTimeout(() => setUploadProgress(null), 2000);
    }
  };

  // 如果未初始化，显示加载状态
  if (!isInitialized) {
    return (
      <div className="fixed top-4 right-4 bg-white shadow-lg rounded-lg border z-50 p-4">
        <div className="flex items-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
          <span className="text-sm text-gray-600">初始化日志系统...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <div
        ref={panelRef}
        {...bind()}
        className={`fixed top-4 right-4 bg-white shadow-lg rounded-lg border z-50 cursor-move transition-all duration-200 ${
          isMinimized ? 'w-12 h-12' : 'w-64'
        }`}
        style={{ 
          transform: `translate(${position.x}px, ${position.y}px)`,
          touchAction: 'none'
        }}
      >
        {/* 最小化状态 */}
        {isMinimized ? (
          <div className="w-full h-full flex items-center justify-center">
            <button
              onClick={() => setIsMinimized(false)}
              className="w-8 h-8 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors flex items-center justify-center"
              title="展开日志控制面板"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            </button>
          </div>
        ) : (
          /* 展开状态 */
          <div className="p-4">
            {/* 标题栏 */}
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-semibold text-gray-800">日志控制</h3>
              <button
                onClick={() => setIsMinimized(true)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="最小化"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
            </div>

            {/* 记录状态指示器 */}
            <div className="flex items-center space-x-2 mb-3">
              <div className={`w-2 h-2 rounded-full ${isRecording ? 'bg-red-500 animate-pulse' : 'bg-gray-300'}`}></div>
              <span className="text-xs text-gray-600">
                {isRecording ? '正在记录日志和屏幕' : '未记录'}
              </span>
            </div>

            {/* 控制按钮 */}
            <div className="space-y-2">
              {/* 统一记录控制 */}
              <button
                onClick={isRecording ? onStopRecording : onStartRecording}
                className={`w-full px-3 py-2 text-sm rounded-md transition-colors ${
                  isRecording
                    ? 'bg-red-500 text-white hover:bg-red-600'
                    : 'bg-green-500 text-white hover:bg-green-600'
                }`}
                disabled={isLoading}
              >
                {isRecording ? '停止记录' : '开始记录'}
              </button>

              {/* 导出当前会话 */}
              <button
                onClick={handleExportCurrent}
                className="w-full px-3 py-2 text-sm bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors disabled:bg-gray-300"
                disabled={isLoading}
              >
                导出当前会话
              </button>

              {/* 导出历史日志 */}
              <button
                onClick={() => setShowHistoryModal(true)}
                className="w-full px-3 py-2 text-sm bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors disabled:bg-gray-300"
                disabled={isLoading}
              >
                导出历史日志
              </button>

              {/* 上传到服务器 */}
              <button
                onClick={handleUploadToServer}
                className="w-full px-3 py-2 text-sm bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors disabled:bg-gray-300"
                disabled={isLoading}
              >
                上传到服务器
              </button>
            </div>

            {/* 上传进度 */}
            {uploadProgress && (
              <div className="mt-3 p-2 bg-gray-50 rounded-md">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-gray-600">上传进度</span>
                  <span className="text-xs text-gray-600">{Math.round(uploadProgress.percentage)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div
                    className={`h-1 rounded-full transition-all duration-300 ${
                      uploadProgress.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                    }`}
                    style={{ width: `${uploadProgress.percentage}%` }}
                  ></div>
                </div>
                {uploadProgress.message && (
                  <p className="text-xs text-gray-500 mt-1">{uploadProgress.message}</p>
                )}
              </div>
            )}

            {/* 加载指示器 */}
            {isLoading && (
              <div className="mt-3 flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                <span className="ml-2 text-xs text-gray-600">处理中...</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 通知 */}
      {notification && (
        <div className={`fixed top-20 right-4 p-3 rounded-md shadow-lg z-50 ${
          notification.type === 'success' ? 'bg-green-500 text-white' :
          notification.type === 'error' ? 'bg-red-500 text-white' :
          'bg-blue-500 text-white'
        }`}>
          <p className="text-sm">{notification.message}</p>
        </div>
      )}

      {/* 历史导出模态框 */}
      <HistoryExportModal
        isOpen={showHistoryModal}
        onClose={() => setShowHistoryModal(false)}
        onExport={handleExportHistory}
      />
    </>
  );
};
